// Generic outbound agent that handles CSV, API, and XML processing through collection-based handlers
const logger = require('../config/logger');
const PerformanceMonitor = require('../services/performance.service');
// Collection processing configuration
const config = require('../config/config');
const COLLECTION_TIMEOUT = config.messageProcessing.collectionTimeout;
const SHUTDOWN_TIMEOUT = config.messageProcessing.shutdownTimeout;

const {
  validateAgentConfig,
  logAgentActivity,
  loadMappingConfig
} = require('../helpers/agent.helper');
const path = require('path');
const handlers = require(path.join(process.cwd(), 'handlers'));

/**
 * Sets up collection-based queue listener for all outbound agents
 * @param {Object} agent - Agent configuration
 * @param {Function} handlerFunction - Handler function to process messages
 * @param {Object} performanceMonitor - Performance monitor instance
 * @returns {Promise<void>}
 */
async function setupCollectionQueueListener(agent, handlerFunction, performanceMonitor) {
  // Global state for graceful shutdown
  let isShuttingDown = false;
  let activeProcessingPromises = new Set();
  let messageCollection = [];
  let collectionTimer = null;
  let collectionCounter = 0;
  let channel = null;
  let consumerTag = null;

  // Graceful shutdown function
  const gracefulShutdown = async (signal) => {
    if (isShuttingDown) {
      logger.warn(`Already shutting down, ignoring ${signal}`);
      return;
    }

    isShuttingDown = true;
    logger.info(`Received ${signal}. Starting graceful shutdown for agent: ${agent.name}...`);

    try {
      // Stop accepting new messages
      if (consumerTag && channel) {
        logger.info(`Cancelling RabbitMQ consumer for agent: ${agent.name}...`);
        await channel.cancel(consumerTag);
      }

      // Clear collection timer
      if (collectionTimer) {
        clearTimeout(collectionTimer);
        collectionTimer = null;
      }

      // Process any remaining messages in collection
      if (messageCollection.length > 0) {
        logger.info(`Processing remaining ${messageCollection.length} messages in collection for agent: ${agent.name}...`);
        await processCollection(messageCollection);
        messageCollection = [];
      }

      // Wait for active processing to complete with timeout
      logger.info(`Waiting for ${activeProcessingPromises.size} active processing operations to complete (timeout: ${SHUTDOWN_TIMEOUT}ms)...`);

      if (activeProcessingPromises.size > 0) {
        const timeoutPromise = new Promise((resolve) => setTimeout(resolve, SHUTDOWN_TIMEOUT));
        const activePromise = Promise.all(Array.from(activeProcessingPromises));

        await Promise.race([activePromise, timeoutPromise]);

        if (activeProcessingPromises.size > 0) {
          logger.warn(`Shutdown timeout reached. ${activeProcessingPromises.size} operations still active.`);
        }
      }

      // Close RabbitMQ channel
      if (channel) {
        logger.info(`Closing RabbitMQ channel for agent: ${agent.name}...`);
        await channel.close();
      }

      logger.info(`Graceful shutdown completed for agent: ${agent.name}`);
      process.exit(0);

    } catch (error) {
      logger.error(`Error during graceful shutdown for agent: ${agent.name}:`, error);
      process.exit(1);
    }
  };

  // Setup signal handlers
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  process.on('SIGHUP', () => gracefulShutdown('SIGHUP'));

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    logger.error(`Uncaught Exception in agent ${agent.name}:`, error);
    gracefulShutdown('uncaughtException');
  });

  process.on('unhandledRejection', (reason) => {
    logger.error(`Unhandled Rejection in agent ${agent.name}:`, reason);
    gracefulShutdown('unhandledRejection');
  });

  // Function to process a complete collection
  const processCollection = async (collection) => {
    console.log(`in processCollection with ${collection.length} messages`);
    if (collection.length === 0) return;

    collectionCounter++;

    logger.info(`[COLLECTION] Starting collection processing for agent: ${agent.name} with ${collection.length} messages`);

    try {
      // Get the appropriate collection handler based on handler type
      const collectionHandlerFunction = handlerFunction;
      const mappingConfig = loadMappingConfig(agent.mapping);

      // Process the collection
      const result = await collectionHandlerFunction({
        eventCollection: collection,
        agent,
        mappingConfig,
        performanceMonitor
      });

      logger.info(`[COLLECTION] Collection processing completed for agent: ${agent.name}`, {
        totalEvents: result.totalEvents || collection.length,
        successfulRecords: result.successfulRecords || 0,
        failedRecords: result.failedRecords || 0,
        filesGenerated: result.filesGenerated || 0,
        apiCalls: result.apiCalls || 0
      });

      // Handle individual message acknowledgments based on processing results
      await handleMessageAcknowledgments(collection, result, channel, isShuttingDown, agent.name);

    } catch (error) {
      logger.error(`[COLLECTION] Error processing collection for agent: ${agent.name}:`, error);

      // On collection processing error, nack all messages
      for (const { msg, messageId } of collection) {
        if (channel && !isShuttingDown) {
          // channel.nack(msg, false, false); // Don't requeue on error - commented for now
          logger.debug(`[COLLECTION] Would nack message ${messageId} due to collection error for agent: ${agent.name}`);
        }
      }
    }
  };

  // Function to handle individual message acknowledgments
  const handleMessageAcknowledgments = async (collection, result, channel, isShuttingDown, agentName) => {
    if (!channel || isShuttingDown) return;

    // Get message acknowledgment details from result
    const messageResults = result.messageResults || [];

    if (messageResults.length === 0) {
      // If no individual message results, use overall success/failure
      const overallSuccess = (result.failedRecords || 0) === 0;

      for (const { msg, messageId } of collection) {
        if (overallSuccess) {
          // channel.ack(msg);
          logger.debug(`[ACK] Acknowledged message ${messageId} for agent: ${agentName} (overall success)`);
        } else {
          // channel.nack(msg, false, false); // Commented negative ack for failed records
          logger.debug(`[NACK] Would nack message ${messageId} for agent: ${agentName} (overall failure)`);
        }
      }
    } else {
      // Handle individual message acknowledgments based on detailed results
      for (const { msg, messageId } of collection) {
        const messageResult = messageResults.find(mr => mr.messageId === messageId);

        if (messageResult && messageResult.success) {
          // channel.ack(msg);
          logger.debug(`[ACK] Acknowledged message ${messageId} for agent: ${agentName} (individual success)`);
        } else {
          // channel.nack(msg, false, false); // Commented negative ack for failed records
          logger.debug(`[NACK] Would nack message ${messageId} for agent: ${agentName} (individual failure)`);
        }
      }
    }
  };

  // Function to trigger collection processing
  const triggerCollectionProcessing = async () => {
    console.log(`in triggerCollectionProcessing with ${messageCollection.length} messages`);
    if (messageCollection.length > 0) {
      const currentCollection = [...messageCollection];
      messageCollection = []; // Clear the collection

      if (collectionTimer) {
        clearTimeout(collectionTimer);
        collectionTimer = null;
      }
      await processCollection(currentCollection);
    }
  };

  try {
    logAgentActivity(agent.name, `Setting up collection-based queue listener for: ${agent.queue}`);

    // Setup queue consumer and use existing global channel from index.js
    channel = global.channel;

    if (!channel) {
      throw new Error('RabbitMQ channel not available. Make sure RabbitMQ connection is established in index.js');
    }

    // Assert queue exists
    await channel.assertQueue(agent.queue, { durable: true });

    logger.info(`[Collection Agent] Setting up consumer for queue: ${agent.queue}`);

    // Set up consumer with collection logic
    const consumerInfo = await channel.consume(agent.queue, async (msg) => {
      if (!msg) return;

      // Don't accept new messages during shutdown
      if (isShuttingDown) {
        logger.debug(`Rejecting new message due to shutdown for agent: ${agent.name}`);
        channel.nack(msg, false, true); // requeue = true
        return;
      }

      try {
        const raw = msg.content.toString();
        let event;
        try {
          event = JSON.parse(raw);
        } catch (_e) {
          event = raw;
        }

        const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

        // Add message to current collection
        messageCollection.push({ msg, event, messageId });

        // Set/reset timer for collection timeout
        if (collectionTimer) {
          clearTimeout(collectionTimer);
        }
        collectionTimer = setTimeout(triggerCollectionProcessing, COLLECTION_TIMEOUT);

      } catch (err) {
        logger.error(`Error adding message to collection for agent ${agent.name}:`, err);
        // Process individual message on collection error
        channel.nack(msg, false, false);
      }
    }, {
      noAck: false // Ensure manual acknowledgment
    });

    // Store consumer tag for graceful shutdown
    consumerTag = consumerInfo.consumerTag;

    logger.info(`Collection-based queue listener established for agent: ${agent.name} on queue: ${agent.queue}`);
    logger.info(`Collection timeout: ${COLLECTION_TIMEOUT}ms, Shutdown timeout: ${SHUTDOWN_TIMEOUT}ms`);

  } catch (error) {
    logger.error(`Failed to setup collection queue listener for agent ${agent.name} on queue ${agent.queue}:`, error);
    throw error;
  }
}

/**
 * Main outbound agent handler - routes to appropriate processing based on agent configuration
 * @param {Object} agent - Agent configuration
 * @returns {Promise<void>}
 */
async function outboundAgentHandler(agent) {
  const performanceMonitor = new PerformanceMonitor(`Outbound Processing - ${agent.name}`);

  try {
    logAgentActivity(agent.name, 'Starting outbound processing', {
      type: agent.type,
      source: agent.source,
      handler: agent.handler
    });

    // Validate agent configuration
    if (!validateAgentConfig(agent)) {
      throw new Error(`Invalid agent configuration for ${agent.name}`);
    }

    // Get the appropriate handler
    const handlerFunction = handlers[agent.handler];
    if (!handlerFunction || typeof handlerFunction !== 'function') {
      throw new Error(`Handler '${agent.handler}' not found`);
    }

    performanceMonitor.startStep('Handler Execution', {
      agentName: agent.name,
      handlerName: agent.handler,
      source: agent.source
    });

    // All handlers now use collection-based queue listening approach
    await setupCollectionQueueListener(agent, handlerFunction, performanceMonitor);

  } catch (error) {
    logger.error(`Error in outbound agent processing for ${agent.name}:`, error.message);

    performanceMonitor.complete({
      status: 'error',
      error: error.message,
      agentName: agent.name
    });

    throw error;
  }
}

module.exports = outboundAgentHandler;
