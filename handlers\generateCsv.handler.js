const fs = require('fs');
const path = require('path');
const csv = require('fast-csv');
const logger = require('../config/logger');
const { uploadFile } = require('../services/csv.service');

/**
 * Determines if mapping values are column names (strings) or numerical positions (numbers)
 * @param {Object} mappingConfig - Mapping configuration object
 * @returns {Object} Object with type ('column' or 'position') and processed mapping
 */
function analyzeMappingType(mappingConfig) {
    const values = Object.values(mappingConfig);

    // Check if all values are numbers (as strings or actual numbers)
    const allNumbers = values.every(value => {
        const num = Number(value);
        return !isNaN(num) && isFinite(num);
    });

    if (allNumbers) {
        // Convert string numbers to actual numbers and sort by position
        const sortedEntries = Object.entries(mappingConfig)
            .map(([key, value]) => [key, Number(value)])
            .sort((a, b) => a[1] - b[1]);

        return {
            type: 'position',
            mapping: Object.fromEntries(sortedEntries)
        };
    } else {
        return {
            type: 'column',
            mapping: mappingConfig
        };
    }
}

/**
 * Collection-based CSV generation handler that processes multiple events in batches
 * @param {Object} params - Handler parameters
 * @param {Array} params.eventCollection - Collection of events to process
 * @param {Object} params.agent - Agent configuration
 * @param {Object} params.performanceMonitor - Performance monitoring instance
 * @returns {Promise<Object>} Collection processing results
 */
async function generateCsv({ eventCollection, agent, mappingConfig, performanceMonitor = null }) {
    const collectionResults = {
        agentName: agent.name,
        totalEvents: eventCollection.length,
        totalRecords: 0,
        successfulRecords: 0,
        failedRecords: 0,
        filesGenerated: 0,
        errors: [],
        processedFiles: [],
        messageResults: [] // Track individual message success/failure
    };

    // try {
    logger.info(`[CSV Collection Handler] Starting collection processing for agent: ${agent.name}`, { eventCount: eventCollection.length });

    // Step 1: Load mapping configuration once for the entire collection
    performanceMonitor?.startStep('Load Mapping Configuration', { agentName: agent.name });
    const { type: mappingType, mapping: processedMapping } = analyzeMappingType(mappingConfig);
    performanceMonitor?.endStep('Load Mapping Configuration', { mappingType });

    logger.info(`[CSV Collection Handler] Loaded mapping configuration: ${agent.mapping} (Type: ${mappingType})`, {});

    // Step 2: Aggregate all event data from the collection with message tracking
    performanceMonitor?.startStep('Aggregate Event Data', { eventCount: eventCollection.length });
    const allEventData = [];
    const messageToDataMapping = new Map(); // Track which data belongs to which message

    for (const { event, messageId } of eventCollection) {
        const eventData = event.params || event.data || event;
        if (Array.isArray(eventData)) {
            eventData.forEach((data) => {
                allEventData.push(data);
                messageToDataMapping.set(allEventData.length - 1, messageId);
            });
        } else if (eventData && typeof eventData === 'object') {
            allEventData.push(eventData);
            messageToDataMapping.set(allEventData.length - 1, messageId);
        }
    }

    collectionResults.totalRecords = allEventData.length;
    performanceMonitor?.endStep('Aggregate Event Data', { totalRecords: allEventData.length });

    if (allEventData.length === 0) {
        logger.warn(`[CSV Collection Handler] No data found in event collection for agent: ${agent.name}`, {});
        return collectionResults;
    }

    // Step 3: Generate single CSV file from all collected data
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${agent.name}_collection_${timestamp}.csv`;

    performanceMonitor?.startStep('Transform Collection Data', { totalRecords: allEventData.length });
    const csvData = [];
    if (mappingType === 'column') {
        // Column name mapping: use values as headers
        const headers = Object.values(processedMapping);
        console.log(`########## processedMapping: ${processedMapping}`);
        console.log(processedMapping);

        csvData.push(headers);
        // Transform each record from aggregated event data
        allEventData.forEach((record, dataIndex) => {
            try {
                const csvRow = [];
                if (dataIndex % 1000 == 0) console.log(`########## fieldParts ${dataIndex}: ${fieldParts}`);

                Object.entries(processedMapping).forEach(([modelField]) => {
                    // Handle nested field access (e.g., Identity.email)
                    const fieldParts = modelField.split('.');
                    let value = record;
                    if (dataIndex % 1000 == 0) console.log(`########## fieldParts ${dataIndex}: ${fieldParts}`);

                    for (const part of fieldParts) {
                        value = value?.[part];
                        if (value === undefined) break;
                    }

                    if (dataIndex % 1000 == 0) console.log(`########## value ${dataIndex}: ${value}`);

                    csvRow.push(value !== undefined ? value : '');
                });
                csvData.push(csvRow);
                collectionResults.successfulRecords++;

                // Track successful message processing
                const messageId = messageToDataMapping.get(dataIndex);
                if (messageId) {
                    collectionResults.messageResults.push({
                        messageId,
                        success: true,
                        dataIndex
                    });
                }
            } catch (error) {
                // Track failed message processing
                const messageId = messageToDataMapping.get(dataIndex);
                if (messageId) {
                    collectionResults.messageResults.push({
                        messageId,
                        success: false,
                        error: error.message,
                        dataIndex
                    });
                }
                collectionResults.failedRecords++;
                collectionResults.errors.push(`Failed to process record at index ${dataIndex}: ${error.message}`);
            }
        });
    } else {
        // Position mapping: no headers, order by position
        allEventData.forEach((record, dataIndex) => {
            try {
                const csvRow = [];
                Object.entries(processedMapping).forEach(([modelField, position]) => {
                    // Handle nested field access (e.g., Identity.email)
                    const fieldParts = modelField.split('.');
                    let value = record;

                    for (const part of fieldParts) {
                        value = value?.[part];
                        if (value === undefined) break;
                    }

                    csvRow[position] = value !== undefined ? value : '';
                });
                // Fill any gaps with empty strings
                for (let i = 0; i < csvRow.length; i++) {
                    if (csvRow[i] === undefined) csvRow[i] = '';
                }

                csvData.push(csvRow);
                collectionResults.successfulRecords++;

                // Track successful message processing
                const messageId = messageToDataMapping.get(dataIndex);
                if (messageId) {
                    collectionResults.messageResults.push({
                        messageId,
                        success: true,
                        dataIndex
                    });
                }
            } catch (error) {
                // Track failed message processing
                const messageId = messageToDataMapping.get(dataIndex);
                if (messageId) {
                    collectionResults.messageResults.push({
                        messageId,
                        success: false,
                        error: error.message,
                        dataIndex
                    });
                }
                collectionResults.failedRecords++;
                collectionResults.errors.push(`Failed to process record at index ${dataIndex}: ${error.message}`);
            }
        });
    }

    performanceMonitor?.endStep('Transform Collection Data', { transformedRows: csvData.length - (mappingType === 'column' ? 1 : 0) });

    // Step 4: Generate and upload CSV file
    const tempDir = './downloads/temp';
    if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
    }
    const tempFilePath = path.join(tempDir, filename);

    performanceMonitor?.startStep('Generate Collection CSV File', { tempFilePath, rowCount: csvData.length });

    try {
        await new Promise((resolve, reject) => {
            const writeStream = fs.createWriteStream(tempFilePath);
            csv.write(csvData, { headers: false })
                .pipe(writeStream)
                .on('finish', resolve)
                .on('error', reject);
        });
        performanceMonitor?.endStep('Generate Collection CSV File', { fileSize: fs.statSync(tempFilePath).size });
    } catch (error) {
        performanceMonitor?.endStep('Generate Collection CSV File', { success: false, error: error.message });

        // Mark all messages as failed due to CSV generation failure
        collectionResults.messageResults.forEach(messageResult => {
            messageResult.success = false;
            messageResult.error = `CSV generation failed: ${error.message}`;
        });

        collectionResults.errors.push(`Failed to generate CSV file: ${error.message}`);
        collectionResults.failedRecords = collectionResults.totalRecords;
        collectionResults.successfulRecords = 0;

        return collectionResults;
    }

    const dataRows = mappingType === 'column' ? csvData.length - 1 : csvData.length;
    logger.info(`[CSV Collection Handler] Generated collection CSV file: ${tempFilePath}`, { dataRows });

    // Step 5: Upload file to destination
    performanceMonitor?.startStep('Upload Collection CSV', { destination: agent.source });
    const uploadResult = await uploadFile(tempFilePath, agent, performanceMonitor, 'CSV');
    performanceMonitor?.endStep('Upload Collection CSV', { success: uploadResult.success });

    // Step 6: Clean up temporary file
    try {
        fs.unlinkSync(tempFilePath);
        logger.info(`[CSV Collection Handler] Cleaned up temporary file: ${tempFilePath}`, {});
    } catch (error) {
        logger.warn(`[CSV Collection Handler] Failed to clean up temporary file ${tempFilePath}: ${error.message}`, {});
    }

    if (!uploadResult.success) {
        collectionResults.errors.push(`Failed to upload CSV to ${agent.source}: ${uploadResult.error}`);

        // Mark all messages as failed due to upload failure
        collectionResults.messageResults.forEach(messageResult => {
            messageResult.success = false;
            messageResult.error = `Upload failed: ${uploadResult.error}`;
        });

        collectionResults.failedRecords = collectionResults.totalRecords;
        collectionResults.successfulRecords = 0;
    } else {
        collectionResults.filesGenerated = 1;
        collectionResults.processedFiles.push({
            filename,
            uploadPath: uploadResult.uploadedPath,
            recordCount: dataRows
        });

        // All messages that were successfully processed remain successful
        // (messageResults already contain the correct success status from processing)
    }

    logger.info(`[CSV Collection Handler] Collection processing completed for agent: ${agent.name}`, {
        totalEvents: collectionResults.totalEvents,
        totalRecords: collectionResults.totalRecords,
        successfulRecords: collectionResults.successfulRecords,
        filesGenerated: collectionResults.filesGenerated
    });

    return collectionResults;

    // } catch (error) {
    //     logger.error(`[CSV Collection Handler] Error processing collection for agent: ${agent.name}:`, error.message || error);
    //     collectionResults.errors.push(error.message || error);
    //     collectionResults.failedRecords = collectionResults.totalRecords;
    //     collectionResults.successfulRecords = 0;
    //     return collectionResults;
    // }
}

module.exports = generateCsv;
